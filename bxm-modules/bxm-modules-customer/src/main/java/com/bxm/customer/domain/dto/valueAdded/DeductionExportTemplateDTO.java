package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 扣款操作导出模板DTO
 *
 * 用于导出扣款操作的Excel模板，继承基类的共同字段，并包含扣款操作特有字段：
 * 交付结果
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@ApiModel("扣款操作导出模板DTO")
public class DeductionExportTemplateDTO extends BaseExportTemplateDTO {

    /** 交付结果 */
    @Excel(name = "交付结果", sort = 4)
    @ApiModelProperty(value = "交付结果（当前状态）")
    private String status;
}

package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * 交付操作导出模板DTO
 *
 * 用于导出交付操作的Excel模板，继承基类的共同字段，并包含交付操作特有字段：
 * 交付结果、总扣缴金额
 *
 * <AUTHOR>
 * @date 2025-08-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@ApiModel("交付操作导出模板DTO")
public class DeliveryExportTemplateDTO extends BaseExportTemplateDTO {

    /** 交付结果 */
    @Excel(name = "交付结果", sort = 4)
    @ApiModelProperty(value = "交付结果（当前状态）")
    private String status;

    /** 总扣缴金额 */
    @Excel(name = "总扣缴金额", sort = 5)
    @ApiModelProperty(value = "总扣缴金额")
    private BigDecimal totalWithholdingAmount;
}
